// react-router-dom components
import { Link } from "react-router-dom";

// prop-types is a library for typechecking of props
import PropTypes from "prop-types";

// @mui material components
import Card from "@mui/material/Card";
import Icon from "@mui/material/Icon";
import Grid from "@mui/material/Grid";
import CircularProgress from "@mui/material/CircularProgress";

// Material Dashboard 2 PRO React components
import MDBox from "@/components/MDBox";
import MDTypography from "@/components/MDTypography";
import MDButton from "@/components/MDButton";
import premiumTag from "@/assets/images/premium-tag.png";
import {useTranslation} from "react-i18next";

function DefaultPricingCard({ color, badge, price, specifications, moreSpecifications, action, shadow, title, footer }) {
  const {t} = useTranslation();
  const renderSpecifications = (specs) => {
    return specs.filter(s => !s.hide).map(({ label, includes, highlight, understate, remove_icon }) => (
      <MDBox key={label} display="flex" alignItems="center" p={1}>
        {!remove_icon && <MDBox
          display="flex"
          justifyContent="center"
          alignItems="center"
          width="1.5rem"
          height="1.5rem"
          mr={2}
          mt={-0.125}
        >
          <MDTypography
            variant="body1"
            color={color === "white" ? "text" : "white"}
            sx={{ lineHeight: 0 }}
        >
            {includes 
            ? (highlight ? <Icon color="white">done_all</Icon> : <Icon color={understate ? "secondary" :"white"}>done</Icon>)
            : <Icon color="warning">remove_circle</Icon>}
          </MDTypography>
        </MDBox>}
        <MDTypography
          variant={highlight ? "caption": "button"}
          color={(color === "white" ? (highlight ? "dark" : "text") : (highlight ? "white" : (understate ? "text" : "white")))}
          fontWeight={highlight ? "regular" : "regular"}
          lineHeight={1.5}
          sx={{
            fontSize: "15px"
          }}
        >
          {label}
        </MDTypography>
      </MDBox>
    ));
  }

  return (
    <Card sx={{ boxShadow: ({ boxShadows: { lg } }) => (shadow ? lg : "none") }}>
      <MDBox
        bgColor={color}
        variant={color === "white" ? "contained" : "gradient"}
        borderRadius={!!title && title.title ? "" : "xl"} 
        sx={{minHeight: "400px", position: "relative"}}
      >
        {badge.label != "" && <MDBox
          bgColor={badge.color}
          width="max-content"
          px={4}
          pt={0}
          pb={0.5}
          mx="auto"
          mt={-1.375}
          borderRadius="section"
          lineHeight={1}
          letterSpacing={0.8}
          sx={{border: "1px solid", borderColor: badge.color === "light" ? "dark" : "white"}}
        >
          <MDBox display="flex" alignItems="center" justifyContent="center" gap={0.5}>
            <MDTypography
              variant="caption"
              textTransform="uppercase"
              fontWeight="medium"
              color={badge.color === "light" ? "dark" : "white"}
              sx={{ whiteSpace: "nowrap" }}
            >
              {badge.label}
            </MDTypography>
            {badge.hasIcon && (
              <MDBox
                component="img"
                src={premiumTag}
                alt="Brand"
                width="1rem"
                sx={{ flexShrink: 0 }}
              />
            )}
          </MDBox>
        </MDBox>}
        <MDBox pt={3} pb={2} px={2} textAlign="center">
          {!!title && title.title && <MDTypography variant="body" fontWeight="regular" color="white">
              {title.title}
          </MDTypography>}
          {!!title && title.subtitle && <MDTypography variant="body2"  color="white" mb={2}>
              {title.subtitle}
          </MDTypography>}
          <MDBox my={1}>
            {price.type == "text"
              ? (<MDTypography variant="h2" color={color === "white" ? "dark" : "white"} fontWeight="medium" py={1}>
                  {price.value}
              </MDTypography>)
              : <MDBox color={color === "white" ? "dark" : "white"}>
                  <MDTypography
                    display="inline"
                    component="small"
                    variant="h5"
                    color="inherit"
                    verticalAlign="top"
                  >
                    {price.currency}
                  </MDTypography>
                  {price.strikeValue && 
                    <MDTypography display="inline" component="small" variant="h4" color={color === "white" ? "secondary" : "white"} fontWeight="regular">
                      <s>{price.strikeValue}</s>&nbsp;
                    </MDTypography>
                  }
                    <MDTypography color={!!price.strikeValue ? "warning" : "inherit"} variant="h1" display="inline">
                      {price.value}
                    </MDTypography>
                  <MDTypography display="inline" component="small" variant="h5" color="inherit">
                    /{t(price.type)}
                  </MDTypography>
                  {!!price.subtext && <><br/><MDTypography display="block" component="small" variant="button" fontWeight="medium" color="light" sx={{color:"#33c6ab !important"}}>
                    {" " + price.subtext}
                  </MDTypography></>}
              </MDBox>
            }
          </MDBox>
        </MDBox>
        <MDBox pb={3} px={3} >
            {moreSpecifications && <Grid container justifyContent="space-between" display="flex">
                <Grid item xs={6}>
                  {renderSpecifications(specifications)}
                </Grid>
                <Grid item xs={6}>
                  {renderSpecifications(moreSpecifications)}
                </Grid>
            </Grid>}
            {!moreSpecifications && renderSpecifications(specifications)}
          {action.type === "internal" && (
            <MDBox mt={3}>
              <MDButton
                component={Link}
                to={action.route}
                disabled={action.disabled}
                onClick={action.handler}
                variant="gradient"
                color={(color === "white" && action.disabled) ? "light" : action.color}
                fullWidth
              >
                {action.label}&nbsp;
                {!!action.hasIcon && <Icon sx={{ fontWeight: "bold" }}>arrow_forward</Icon>}
              </MDButton>
            </MDBox>
          )}
          {action.type == "external" && (
            <MDBox mt={3}>
              <MDButton
                component="a"
                href={action.route}
                onClick={action.handler}
                target="_blank"
                rel="noreferrer"
                variant="gradient"
                color={action.color}
                fullWidth
              >
                {action.label}&nbsp;
                {!!action.hasIcon && <Icon sx={{ fontWeight: "bold" }}>arrow_forward</Icon>}
              </MDButton>
            </MDBox>
          )}
          {action.type == "api" && (
            <MDBox mt={3}>
              <MDButton
                disabled={action.disabled}
                onClick={action.handler}
                variant="gradient"
                color={action.color}
                fullWidth
              >
                {action.loader && <CircularProgress size={20} color="inherit" sx={{ mr: 1 }} />}
                {action.label}&nbsp;
                {!!action.hasIcon && <MDBox component="img" src={premiumTag} alt="Brand" width="1rem" ml={0.8} />}
              </MDButton>
            </MDBox>
          )}
        </MDBox>
        {footer && <>{footer}</>}
      </MDBox>
    </Card>
  );
}

// Setting default props for the DefaultPricingCard
DefaultPricingCard.defaultProps = {
  color: "white",
  shadow: true,
};

// Typechecking props for the DefaultPricingCard
DefaultPricingCard.propTypes = {
  color: PropTypes.oneOf([
    "primary",
    "secondary",
    "info",
    "success",
    "warning",
    "error",
    "light",
    "dark",
    "white",
  ]),
  badge: PropTypes.shape({
    color: PropTypes.oneOf([
      "primary",
      "secondary",
      "info",
      "success",
      "warning",
      "error",
      "light",
      "dark",
    ]).isRequired,
    label: PropTypes.string.isRequired,
  }).isRequired,
  price: PropTypes.shape({
    currency: PropTypes.string,
    value: PropTypes.oneOfType([PropTypes.string, PropTypes.number]).isRequired,
    type: PropTypes.string.isRequired,
  }),
  footer : PropTypes.node,
  specifications: PropTypes.arrayOf(PropTypes.object).isRequired,
  action: PropTypes.shape({
    type: PropTypes.oneOf(["external", "internal", "api"]).isRequired,
    route: PropTypes.string,
    label: PropTypes.any.isRequired,
    hasIcon: PropTypes.bool,
    loader : PropTypes.bool,
    disabled: PropTypes.bool,
    handler: PropTypes.func,
    color: PropTypes.oneOf([
      "primary",
      "secondary",
      "info",
      "success",
      "warning",
      "error",
      "light",
      "dark",
    ]).isRequired,
  }).isRequired,
  title: PropTypes.shape({
    title: PropTypes.string,
    subtitle: PropTypes.string,
  }),
  shadow: PropTypes.bool,
};

export default DefaultPricingCard;
